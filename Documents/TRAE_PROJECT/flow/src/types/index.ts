// 用户角色类型
export type UserRole = 'admin' | 'dispatcher' | 'customer';

// 节点状态类型
export type NodeStatus = 'pending' | 'processing' | 'completed' | 'error' | 'warning' | 'waiting';

// 节点类型
export type NodeType = 
  | 'start' 
  | 'departure' 
  | 'transit' 
  | 'special' 
  | 'unloading' 
  | 'completed' 
  | 'end'
  | 'self_pickup'
  | 'self_receipt'
  | 'self_return'
  | 'direct_pickup'
  | 'direct_sign'
  | 'direct_return'
  | 'signed_return'
  | 'external_transfer'
  | 'external_fee'
  | 'transfer_sign'
  | 'transfer_return'
  | 'internal_departure'
  | 'cargo_loading'
  | 'info_supplement'
  | 'scheduled_pickup'
  | 'wait_notification'
  | 'custom';

// 用户信息
export interface User {
  id: string;
  name: string;
  role: UserRole;
  email: string;
  avatar?: string;
  permissions: string[];
}

// 流程节点
export interface FlowNode {
  id: string;
  type: NodeType;
  name: string;
  description: string;
  status: NodeStatus;
  position: { x: number; y: number };
  data: {
    count: number;
    lastUpdated?: string;
  };
  permissions: {
    viewRoles: UserRole[];
    editRoles: UserRole[];
  };
  style?: {
    backgroundColor?: string;
    borderColor?: string;
    textColor?: string;
  };
}

// 连接线
export interface FlowEdge {
  id: string;
  source: string;
  target: string;
  type?: string;
  animated?: boolean;
  style?: {
    stroke?: string;
    strokeWidth?: number;
  };
}

// 运单信息
export interface Waybill {
  id: string;
  orderNumber: string;
  currentNodeId: string;
  status: NodeStatus;
  createTime: string;
  updateTime: string;
  estimatedDelivery: string;
  origin: string;
  destination: string;
  weight: number;
  volume: number;
  customer: {
    name: string;
    phone: string;
  };
  driver?: {
    name: string;
    phone: string;
    vehicleNumber: string;
  };
  history: {
    nodeId: string;
    timestamp: string;
    operator: string;
    remark?: string;
  }[];
}

// 统计数据
export interface Statistics {
  totalWaybills: number;
  completedWaybills: number;
  processingWaybills: number;
  errorWaybills: number;
  avgDeliveryTime: number;
  nodeStats: {
    nodeId: string;
    nodeName: string;
    count: number;
    successRate: number;
    avgProcessTime: number;
  }[];
  dailyStats: {
    date: string;
    completed: number;
    created: number;
    errors: number;
  }[];
}

// 节点编辑表单数据
export interface NodeEditForm {
  name: string;
  description: string;
  type: NodeType;
  permissions: {
    viewRoles: UserRole[];
    editRoles: UserRole[];
  };
  style: {
    backgroundColor: string;
    borderColor: string;
    textColor: string;
  };
}

// 应用状态
export interface AppState {
  currentUser: User | null;
  nodes: FlowNode[];
  edges: FlowEdge[];
  waybills: Waybill[];
  statistics: Statistics;
  selectedNodeId: string | null;
  isEditMode: boolean;
}