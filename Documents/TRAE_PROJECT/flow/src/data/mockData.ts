import { User, FlowNode, FlowEdge, Waybill, Statistics } from '../types';

// Mock用户数据
export const mockUsers: User[] = [
  {
    id: '1',
    name: '张管理员',
    role: 'admin',
    email: '<EMAIL>',
    avatar: 'https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=professional%20business%20avatar%20male%20manager&image_size=square',
    permissions: ['view_all', 'edit_all', 'manage_users', 'manage_permissions']
  },
  {
    id: '2',
    name: '李调度员',
    role: 'dispatcher',
    email: '<EMAIL>',
    avatar: 'https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=professional%20business%20avatar%20female%20dispatcher&image_size=square',
    permissions: ['view_operations', 'edit_operations', 'view_statistics']
  },
  {
    id: '3',
    name: '王客户',
    role: 'customer',
    email: '<EMAIL>',
    avatar: 'https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=friendly%20customer%20avatar%20business%20casual&image_size=square',
    permissions: ['view_basic', 'view_own_orders']
  }
];

// Mock流程节点数据
export const mockNodes: FlowNode[] = [
  {
    id: 'start',
    type: 'start',
    name: '开始',
    description: '运单创建起始节点',
    status: 'completed',
    position: { x: 100, y: 400 },
    data: {
      count: 156
    },
    permissions: {
      viewRoles: ['admin', 'dispatcher', 'customer'],
      editRoles: ['admin']
    },
    style: {
      backgroundColor: '#1E3A8A',
      borderColor: '#1E40AF',
      textColor: '#FFFFFF'
    }
  },
  {
    id: 'departure',
    type: 'departure',
    name: '发车',
    description: '货物装车发车节点',
    status: 'processing',
    position: { x: 450, y: 400 },
    data: {
      count: 142
    },
    permissions: {
      viewRoles: ['admin', 'dispatcher', 'customer'],
      editRoles: ['admin', 'dispatcher']
    },
    style: {
      backgroundColor: '#1E3A8A',
      borderColor: '#1E40AF',
      textColor: '#FFFFFF'
    }
  },
  {
    id: 'transit',
    type: 'transit',
    name: '在途',
    description: '货物运输途中节点',
    status: 'processing',
    position: { x: 800, y: 400 },
    data: {
      count: 128
    },
    permissions: {
      viewRoles: ['admin', 'dispatcher', 'customer'],
      editRoles: ['admin', 'dispatcher']
    },
    style: {
      backgroundColor: '#1E3A8A',
      borderColor: '#1E40AF',
      textColor: '#FFFFFF'
    }
  },
  {
    id: 'special',
    type: 'special',
    name: '待卸',
    description: '待卸处理节点',
    status: 'warning',
    position: { x: 1150, y: 400 },
    data: {
      count: 12
    },
    permissions: {
      viewRoles: ['admin', 'dispatcher'],
      editRoles: ['admin']
    },
    style: {
      backgroundColor: '#1E3A8A',
      borderColor: '#1E40AF',
      textColor: '#FFFFFF'
    }
  },
  {
    id: 'unloading',
    type: 'unloading',
    name: '在卸',
    description: '货物卸载节点',
    status: 'processing',
    position: { x: 1500, y: 400 },
    data: {
      count: 89
    },
    permissions: {
      viewRoles: ['admin', 'dispatcher', 'customer'],
      editRoles: ['admin', 'dispatcher']
    },
    style: {
      backgroundColor: '#1E3A8A',
      borderColor: '#1E40AF',
      textColor: '#FFFFFF'
    }
  },
  {
    id: 'completed',
    type: 'completed',
    name: '已卸',
    description: '货物卸载完成节点',
    status: 'completed',
    position: { x: 1850, y: 400 },
    data: {
      count: 87
    },
    permissions: {
      viewRoles: ['admin', 'dispatcher', 'customer'],
      editRoles: ['admin']
    },
    style: {
      backgroundColor: '#10B981',
      borderColor: '#059669',
      textColor: '#FFFFFF'
    }
  },
  {
    id: 'self_pickup',
    type: 'self_pickup',
    name: '自提收货',
    description: '客户自提收货节点',
    status: 'processing',
    position: { x: 2200, y: 200 },
    data: {
      count: 23
    },
    permissions: {
      viewRoles: ['admin', 'dispatcher', 'customer'],
      editRoles: ['admin', 'dispatcher']
    },
    style: {
      backgroundColor: '#F59E0B',
      borderColor: '#D97706',
      textColor: '#FFFFFF'
    }
  },
  {
    id: 'self_receipt',
    type: 'self_receipt',
    name: '自提回单',
    description: '自提回单确认节点',
    status: 'completed',
    position: { x: 2550, y: 200 },
    data: {
      count: 21
    },
    permissions: {
      viewRoles: ['admin', 'dispatcher', 'customer'],
      editRoles: ['admin']
    },
    style: {
      backgroundColor: '#10B981',
      borderColor: '#059669',
      textColor: '#FFFFFF'
    }
  },
  {
    id: 'direct_pickup',
    type: 'direct_pickup',
    name: '直派收货',
    description: '直派收货节点',
    status: 'processing',
    position: { x: 2200, y: 600 },
    data: {
      count: 45
    },
    permissions: {
      viewRoles: ['admin', 'dispatcher'],
      editRoles: ['admin', 'dispatcher']
    },
    style: {
      backgroundColor: '#F59E0B',
      borderColor: '#D97706',
      textColor: '#FFFFFF'
    }
  },
  {
    id: 'direct_sign',
    type: 'direct_sign',
    name: '直派签收',
    description: '直派签收确认节点',
    status: 'processing',
    position: { x: 2550, y: 600 },
    data: {
      count: 42
    },
    permissions: {
      viewRoles: ['admin', 'dispatcher'],
      editRoles: ['admin', 'dispatcher']
    },
    style: {
      backgroundColor: '#10B981',
      borderColor: '#059669',
      textColor: '#FFFFFF'
    }
  },
  {
    id: 'direct_receipt',
    type: 'direct_pickup',
    name: '直派回单',
    description: '直派回单确认节点',
    status: 'completed',
    position: { x: 2900, y: 600 },
    data: {
      count: 40
    },
    permissions: {
      viewRoles: ['admin', 'dispatcher'],
      editRoles: ['admin']
    },
    style: {
      backgroundColor: '#10B981',
      borderColor: '#059669',
      textColor: '#FFFFFF'
    }
  },
  {
    id: 'sign_receipt',
    type: 'direct_sign',
    name: '签回单',
    description: '签收回单节点',
    status: 'completed',
    position: { x: 3250, y: 600 },
    data: {
      count: 38
    },
    permissions: {
      viewRoles: ['admin', 'dispatcher'],
      editRoles: ['admin']
    },
    style: {
      backgroundColor: '#10B981',
      borderColor: '#059669',
      textColor: '#FFFFFF'
    }
  },
  {
    id: 'external_transfer',
    type: 'external_transfer',
    name: '外转',
    description: '外部转运节点',
    status: 'warning',
    position: { x: 800, y: 800 },
    data: {
      count: 8
    },
    permissions: {
      viewRoles: ['admin', 'dispatcher'],
      editRoles: ['admin']
    },
    style: {
      backgroundColor: '#F59E0B',
      borderColor: '#D97706',
      textColor: '#FFFFFF'
    }
  },
  {
    id: 'external_transfer_fee',
    type: 'external_fee',
    name: '外转费用',
    description: '外转费用计算节点',
    status: 'completed',
    position: { x: 1150, y: 800 },
    data: {
      count: 7
    },
    permissions: {
      viewRoles: ['admin', 'dispatcher'],
      editRoles: ['admin']
    },
    style: {
      backgroundColor: '#10B981',
      borderColor: '#059669',
      textColor: '#FFFFFF'
    }
  },
  {
    id: 'transfer_sign',
    type: 'transfer_sign',
    name: '中转签收',
    description: '中转站签收节点',
    status: 'processing',
    position: { x: 1500, y: 800 },
    data: {
      count: 15
    },
    permissions: {
      viewRoles: ['admin', 'dispatcher'],
      editRoles: ['admin', 'dispatcher']
    },
    style: {
      backgroundColor: '#1E3A8A',
      borderColor: '#1E40AF',
      textColor: '#FFFFFF'
    }
  },
  {
    id: 'transfer_receipt',
    type: 'transfer_sign',
    name: '中转回单',
    description: '中转回单确认节点',
    status: 'completed',
    position: { x: 1850, y: 800 },
    data: {
      count: 14
    },
    permissions: {
      viewRoles: ['admin', 'dispatcher'],
      editRoles: ['admin']
    },
    style: {
      backgroundColor: '#10B981',
      borderColor: '#059669',
      textColor: '#FFFFFF'
    }
  },
  {
    id: 'internal_departure',
    type: 'internal_departure',
    name: '内转发车',
    description: '内部转运发车节点',
    status: 'processing',
    position: { x: 450, y: 800 },
    data: {
      count: 25
    },
    permissions: {
      viewRoles: ['admin', 'dispatcher'],
      editRoles: ['admin', 'dispatcher']
    },
    style: {
      backgroundColor: '#1E3A8A',
      borderColor: '#1E40AF',
      textColor: '#FFFFFF'
    }
  },
  {
    id: 'cargo_loading',
    type: 'cargo_loading',
    name: '货运装载',
    description: '货物装载节点',
    status: 'processing',
    position: { x: 100, y: 600 },
    data: {
      count: 35
    },
    permissions: {
      viewRoles: ['admin', 'dispatcher'],
      editRoles: ['admin', 'dispatcher']
    },
    style: {
      backgroundColor: '#1E3A8A',
      borderColor: '#1E40AF',
      textColor: '#FFFFFF'
    }
  },
  {
    id: 'info_supplement',
    type: 'info_supplement',
    name: '信息补充',
    description: '信息补充完善节点',
    status: 'processing',
    position: { x: 450, y: 600 },
    data: {
      count: 18
    },
    permissions: {
      viewRoles: ['admin', 'dispatcher'],
      editRoles: ['admin', 'dispatcher']
    },
    style: {
      backgroundColor: '#1E3A8A',
      borderColor: '#1E40AF',
      textColor: '#FFFFFF'
    }
  },
  {
    id: 'scheduled_pickup',
    type: 'scheduled_pickup',
    name: '定时收货',
    description: '定时收货节点',
    status: 'processing',
    position: { x: 100, y: 200 },
    data: {
      count: 12,
      lastUpdated: '2024-01-15 13:35:00'
    },
    permissions: {
      viewRoles: ['admin', 'dispatcher'],
      editRoles: ['admin', 'dispatcher']
    },
    style: {
      backgroundColor: '#1E3A8A',
      borderColor: '#1E40AF',
      textColor: '#FFFFFF'
    }
  },
  {
    id: 'wait_notification',
    type: 'wait_notification',
    name: '等通知',
    description: '等待通知节点',
    status: 'waiting',
    position: { x: 400, y: 300 },
    data: {
      count: 6,
      lastUpdated: '2024-01-15 13:20:00'
    },
    permissions: {
      viewRoles: ['admin', 'dispatcher'],
      editRoles: ['admin']
    },
    style: {
      backgroundColor: '#1E3A8A',
      borderColor: '#1E40AF',
      textColor: '#FFFFFF'
    }
  }
];

// Mock连接线数据
export const mockEdges: FlowEdge[] = [
  {
    id: 'start-departure',
    source: 'start',
    target: 'departure',
    type: 'smoothstep',
    animated: true,
    style: { stroke: '#6B7280', strokeWidth: 2 }
  },
  {
    id: 'departure-transit',
    source: 'departure',
    target: 'transit',
    type: 'smoothstep',
    animated: true,
    style: { stroke: '#6B7280', strokeWidth: 2 }
  },
  {
    id: 'transit-special',
    source: 'transit',
    target: 'special',
    type: 'smoothstep',
    style: { stroke: '#6B7280', strokeWidth: 2 }
  },
  {
    id: 'special-unloading',
    source: 'special',
    target: 'unloading',
    type: 'smoothstep',
    style: { stroke: '#6B7280', strokeWidth: 2 }
  },
  {
    id: 'transit-unloading',
    source: 'transit',
    target: 'unloading',
    type: 'smoothstep',
    animated: true,
    style: { stroke: '#6B7280', strokeWidth: 2 }
  },
  {
    id: 'unloading-completed',
    source: 'unloading',
    target: 'completed',
    type: 'smoothstep',
    animated: true,
    style: { stroke: '#6B7280', strokeWidth: 2 }
  },
  {
    id: 'completed-self_pickup',
    source: 'completed',
    target: 'self_pickup',
    type: 'smoothstep',
    style: { stroke: '#6B7280', strokeWidth: 2 }
  },
  {
    id: 'self_pickup-self_receipt',
    source: 'self_pickup',
    target: 'self_receipt',
    type: 'smoothstep',
    animated: true,
    style: { stroke: '#6B7280', strokeWidth: 2 }
  },
  {
    id: 'completed-direct_pickup',
    source: 'completed',
    target: 'direct_pickup',
    type: 'smoothstep',
    style: { stroke: '#6B7280', strokeWidth: 2 }
  },
  {
    id: 'direct_pickup-direct_sign',
    source: 'direct_pickup',
    target: 'direct_sign',
    type: 'smoothstep',
    animated: true,
    style: { stroke: '#6B7280', strokeWidth: 2 }
  },
  {
    id: 'direct_sign-direct_receipt',
    source: 'direct_sign',
    target: 'direct_receipt',
    type: 'smoothstep',
    animated: true,
    style: { stroke: '#6B7280', strokeWidth: 2 }
  },
  {
    id: 'direct_receipt-sign_receipt',
    source: 'direct_receipt',
    target: 'sign_receipt',
    type: 'smoothstep',
    animated: true,
    style: { stroke: '#6B7280', strokeWidth: 2 }
  },
  {
    id: 'transit-external_transfer',
    source: 'transit',
    target: 'external_transfer',
    type: 'smoothstep',
    style: { stroke: '#F59E0B', strokeWidth: 2 }
  },
  {
    id: 'external_transfer-external_transfer_fee',
    source: 'external_transfer',
    target: 'external_transfer_fee',
    type: 'smoothstep',
    style: { stroke: '#6B7280', strokeWidth: 2 }
  },
  {
    id: 'external_transfer_fee-transfer_sign',
    source: 'external_transfer_fee',
    target: 'transfer_sign',
    type: 'smoothstep',
    style: { stroke: '#6B7280', strokeWidth: 2 }
  },
  {
    id: 'transfer_sign-transfer_receipt',
    source: 'transfer_sign',
    target: 'transfer_receipt',
    type: 'smoothstep',
    animated: true,
    style: { stroke: '#6B7280', strokeWidth: 2 }
  },
  {
    id: 'start-cargo_loading',
    source: 'start',
    target: 'cargo_loading',
    type: 'smoothstep',
    style: { stroke: '#6B7280', strokeWidth: 2 }
  },
  {
    id: 'cargo_loading-info_supplement',
    source: 'cargo_loading',
    target: 'info_supplement',
    type: 'smoothstep',
    style: { stroke: '#6B7280', strokeWidth: 2 }
  },
  {
    id: 'info_supplement-departure',
    source: 'info_supplement',
    target: 'departure',
    type: 'smoothstep',
    style: { stroke: '#6B7280', strokeWidth: 2 }
  },
  {
    id: 'start-scheduled_pickup',
    source: 'start',
    target: 'scheduled_pickup',
    type: 'smoothstep',
    style: { stroke: '#6B7280', strokeWidth: 2 }
  },
  {
    id: 'scheduled_pickup-wait_notification',
    source: 'scheduled_pickup',
    target: 'wait_notification',
    type: 'smoothstep',
    style: { stroke: '#6B7280', strokeWidth: 2 }
  },
  {
    id: 'wait_notification-internal_departure',
    source: 'wait_notification',
    target: 'internal_departure',
    type: 'smoothstep',
    style: { stroke: '#6B7280', strokeWidth: 2 }
  }
];

// Mock运单数据
export const mockWaybills: Waybill[] = [
  {
    id: 'wb001',
    orderNumber: 'WB20240115001',
    currentNodeId: 'transit',
    status: 'processing',
    createTime: '2024-01-15 08:00:00',
    updateTime: '2024-01-15 14:20:00',
    estimatedDelivery: '2024-01-16 18:00:00',
    origin: '北京市朝阳区',
    destination: '上海市浦东新区',
    weight: 1500,
    volume: 8.5,
    customer: {
      name: '张三',
      phone: '13800138001'
    },
    driver: {
      name: '李师傅',
      phone: '13900139001',
      vehicleNumber: '京A12345'
    },
    history: [
      {
        nodeId: 'start',
        timestamp: '2024-01-15 08:00:00',
        operator: '系统',
        remark: '运单创建'
      },
      {
        nodeId: 'departure',
        timestamp: '2024-01-15 10:30:00',
        operator: '李调度员',
        remark: '货物装车完成，准备发车'
      },
      {
        nodeId: 'transit',
        timestamp: '2024-01-15 12:00:00',
        operator: '李师傅',
        remark: '车辆已出发，预计明日下午到达'
      }
    ]
  },
  {
    id: 'wb002',
    orderNumber: 'WB20240115002',
    currentNodeId: 'unloading',
    status: 'processing',
    createTime: '2024-01-14 14:00:00',
    updateTime: '2024-01-15 14:15:00',
    estimatedDelivery: '2024-01-15 16:00:00',
    origin: '广州市天河区',
    destination: '深圳市南山区',
    weight: 800,
    volume: 4.2,
    customer: {
      name: '王五',
      phone: '13700137001'
    },
    driver: {
      name: '赵师傅',
      phone: '13800138002',
      vehicleNumber: '粤B67890'
    },
    history: [
      {
        nodeId: 'start',
        timestamp: '2024-01-14 14:00:00',
        operator: '系统',
        remark: '运单创建'
      },
      {
        nodeId: 'departure',
        timestamp: '2024-01-14 16:00:00',
        operator: '李调度员',
        remark: '货物装车完成'
      },
      {
        nodeId: 'transit',
        timestamp: '2024-01-14 18:00:00',
        operator: '赵师傅',
        remark: '车辆出发'
      },
      {
        nodeId: 'unloading',
        timestamp: '2024-01-15 14:00:00',
        operator: '赵师傅',
        remark: '到达目的地，开始卸货'
      }
    ]
  }
];

// Mock统计数据
export const mockStatistics: Statistics = {
  totalWaybills: 156,
  completedWaybills: 87,
  processingWaybills: 64,
  errorWaybills: 5,
  avgDeliveryTime: 28.5,
  nodeStats: [
    {
      nodeId: 'start',
      nodeName: '开始',
      count: 156,
      successRate: 100,
      avgProcessTime: 0.5
    },
    {
      nodeId: 'departure',
      nodeName: '发车',
      count: 142,
      successRate: 95.2,
      avgProcessTime: 2.5
    },
    {
      nodeId: 'transit',
      nodeName: '在途',
      count: 128,
      successRate: 92.8,
      avgProcessTime: 24.5
    },
    {
      nodeId: 'unloading',
      nodeName: '在卸',
      count: 89,
      successRate: 96.5,
      avgProcessTime: 3.2
    },
    {
      nodeId: 'completed',
      nodeName: '已卸',
      count: 87,
      successRate: 100,
      avgProcessTime: 1.0
    }
  ],
  dailyStats: [
    { date: '2024-01-10', completed: 12, created: 15, errors: 1 },
    { date: '2024-01-11', completed: 18, created: 20, errors: 0 },
    { date: '2024-01-12', completed: 15, created: 18, errors: 2 },
    { date: '2024-01-13', completed: 22, created: 25, errors: 1 },
    { date: '2024-01-14', completed: 20, created: 23, errors: 1 },
    { date: '2024-01-15', completed: 16, created: 19, errors: 0 }
  ]
};

// 当前登录用户（默认为管理员）
export const currentUser = mockUsers[0];