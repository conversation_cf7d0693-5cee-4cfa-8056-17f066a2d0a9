import { create } from 'zustand';
import { AppState, User, FlowNode, FlowEdge, Waybill, Statistics } from '../types';
import { mockUsers, mockNodes, mockEdges, mockWaybills, mockStatistics, currentUser } from '../data/mockData';

// localStorage 键名
const NODE_POSITIONS_KEY = 'flow_node_positions';
const EDGES_KEY = 'flow_edges';
const NODES_KEY = 'flow_nodes';

// 从 localStorage 加载节点位置
const loadNodePositions = (): Record<string, { x: number; y: number }> => {
  try {
    const saved = localStorage.getItem(NODE_POSITIONS_KEY);
    return saved ? JSON.parse(saved) : {};
  } catch (error) {
    console.warn('Failed to load node positions from localStorage:', error);
    return {};
  }
};

// 保存节点位置到 localStorage
const saveNodePositions = (positions: Record<string, { x: number; y: number }>) => {
  try {
    localStorage.setItem(NODE_POSITIONS_KEY, JSON.stringify(positions));
  } catch (error) {
    console.warn('Failed to save node positions to localStorage:', error);
  }
};

// 从 localStorage 加载连线数据
const loadEdges = (): FlowEdge[] => {
  try {
    const saved = localStorage.getItem(EDGES_KEY);
    return saved ? JSON.parse(saved) : mockEdges;
  } catch (error) {
    console.warn('Failed to load edges from localStorage:', error);
    return mockEdges;
  }
};

// 保存连线数据到 localStorage
const saveEdges = (edges: FlowEdge[]) => {
  try {
    localStorage.setItem(EDGES_KEY, JSON.stringify(edges));
  } catch (error) {
    console.warn('Failed to save edges to localStorage:', error);
  }
};

// 从 localStorage 加载节点数据
const loadNodes = (): FlowNode[] => {
  try {
    const saved = localStorage.getItem(NODES_KEY);
    return saved ? JSON.parse(saved) : mockNodes;
  } catch (error) {
    console.warn('Failed to load nodes from localStorage:', error);
    return mockNodes;
  }
};

// 保存节点数据到 localStorage
const saveNodes = (nodes: FlowNode[]) => {
  try {
    localStorage.setItem(NODES_KEY, JSON.stringify(nodes));
  } catch (error) {
    console.warn('Failed to save nodes to localStorage:', error);
  }
};

// 应用保存的位置到节点数据
const applyStoredPositions = (nodes: FlowNode[]): FlowNode[] => {
  const storedPositions = loadNodePositions();
  return nodes.map(node => ({
    ...node,
    position: storedPositions[node.id] || node.position
  }));
};

interface AppStore extends AppState {
  // Actions
  setCurrentUser: (user: User | null) => void;
  updateNode: (nodeId: string, updates: Partial<FlowNode>) => void;
  updateNodePosition: (nodeId: string, position: { x: number; y: number }) => void;
  saveNodeChanges: (nodeId: string, changes: Partial<FlowNode>) => Promise<{ success: boolean; error?: string }>;
  selectNode: (nodeId: string | null) => void;
  toggleEditMode: () => void;
  addWaybill: (waybill: Waybill) => void;
  updateWaybill: (waybillId: string, updates: Partial<Waybill>) => void;
  refreshStatistics: () => void;
  switchRole: (role: 'admin' | 'dispatcher' | 'customer') => void;
  resetNodePositions: () => void;
  
  // New editing functions
  addNode: (node: Omit<FlowNode, 'id'>) => string;
  deleteNode: (nodeId: string) => void;
  addEdge: (edge: Omit<FlowEdge, 'id'>) => string;
  deleteEdge: (edgeId: string) => void;
  updateEdge: (edgeId: string, updates: Partial<FlowEdge>) => void;
  
  // Helper functions
  getVisibleNodes: () => FlowNode[];
  getEditableNodes: () => FlowNode[];
  canEditNode: (nodeId: string) => boolean;
  canViewNode: (nodeId: string) => boolean;
}

export const useAppStore = create<AppStore>((set, get) => ({
  // Initial state
  currentUser,
  nodes: applyStoredPositions(loadNodes()),
  edges: loadEdges(),
  waybills: mockWaybills,
  statistics: mockStatistics,
  selectedNodeId: null,
  isEditMode: false,

  // Actions
  setCurrentUser: (user) => set({ currentUser: user }),

  updateNode: (nodeId, updates) => set((state) => ({
    nodes: state.nodes.map(node => 
      node.id === nodeId ? { ...node, ...updates } : node
    )
  })),

  updateNodePosition: (nodeId, position) => {
    set((state) => {
      const updatedNodes = state.nodes.map(node => 
        node.id === nodeId ? { ...node, position } : node
      );
      
      // 保存所有节点位置到 localStorage
      const allPositions = updatedNodes.reduce((acc, node) => {
        acc[node.id] = node.position;
        return acc;
      }, {} as Record<string, { x: number; y: number }>);
      
      saveNodePositions(allPositions);
      
      return { nodes: updatedNodes };
    });
  },

  saveNodeChanges: async (nodeId, updates) => {
    try {
      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // 模拟可能的失败
      if (Math.random() < 0.1) { // 10% 失败率
        return { success: false, error: '网络错误，请重试' };
      }
      
      // 更新节点数据
      set((state) => ({
        nodes: state.nodes.map(node => 
          node.id === nodeId 
            ? { 
                ...node, 
                ...updates,
                data: {
                  ...node.data,
                  lastUpdated: new Date().toLocaleString('zh-CN')
                }
              }
            : node
        )
      }));
      
      return { success: true };
    } catch (error) {
      return { success: false, error: '保存失败，请重试' };
    }
  },

  selectNode: (nodeId) => set({ selectedNodeId: nodeId }),

  toggleEditMode: () => set((state) => ({ isEditMode: !state.isEditMode })),

  addWaybill: (waybill) => set((state) => ({
    waybills: [...state.waybills, waybill]
  })),

  updateWaybill: (waybillId, updates) => set((state) => ({
    waybills: state.waybills.map(waybill => 
      waybill.id === waybillId ? { ...waybill, ...updates } : waybill
    )
  })),

  refreshStatistics: () => {
    const state = get();
    const totalWaybills = state.waybills.length;
    const completedWaybills = state.waybills.filter(w => w.status === 'completed').length;
    const processingWaybills = state.waybills.filter(w => w.status === 'processing').length;
    const errorWaybills = state.waybills.filter(w => w.status === 'error').length;
    
    // Update statistics based on current data
    set({
      statistics: {
        ...state.statistics,
        totalWaybills,
        completedWaybills,
        processingWaybills,
        errorWaybills
      }
    });
  },

  switchRole: (role) => {
    const users = mockUsers;
    const targetUser = users.find(user => user.role === role);
    if (targetUser) {
      set({ currentUser: targetUser });
    }
  },

  resetNodePositions: () => {
    try {
      localStorage.removeItem(NODE_POSITIONS_KEY);
      localStorage.removeItem(EDGES_KEY);
      localStorage.removeItem(NODES_KEY);
      // 重置为默认数据
      set({
        nodes: applyStoredPositions(mockNodes),
        edges: mockEdges
      });
    } catch (error) {
      console.warn('Failed to reset node positions:', error);
    }
  },

  // New editing functions
  addNode: (nodeData) => {
    const newId = `node_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const newNode: FlowNode = {
      id: newId,
      ...nodeData,
      data: {
        count: 10,
        lastUpdated: new Date().toLocaleString('zh-CN'),
        ...nodeData.data
      }
    };
    
    set((state) => {
      const updatedNodes = [...state.nodes, newNode];
      saveNodes(updatedNodes);
      return { nodes: updatedNodes };
    });
    
    return newId;
  },

  deleteNode: (nodeId) => {
    set((state) => {
      const updatedNodes = state.nodes.filter(node => node.id !== nodeId);
      const updatedEdges = state.edges.filter(edge => edge.source !== nodeId && edge.target !== nodeId);
      saveNodes(updatedNodes);
      saveEdges(updatedEdges);
      return {
        nodes: updatedNodes,
        edges: updatedEdges
      };
    });
  },

  addEdge: (edgeData) => {
    const newId = `edge_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const newEdge: FlowEdge = {
      id: newId,
      type: 'smoothstep',
      animated: true,
      style: { stroke: '#6B7280', strokeWidth: 2 },
      ...edgeData
    };
    
    set((state) => {
      const updatedEdges = [...state.edges, newEdge];
      saveEdges(updatedEdges);
      return { edges: updatedEdges };
    });
    
    return newId;
  },

  deleteEdge: (edgeId) => {
    console.log('Store deleteEdge called with:', edgeId);
    set((state) => {
      console.log('Current edges:', state.edges.map(e => e.id));
      const updatedEdges = state.edges.filter(edge => edge.id !== edgeId);
      console.log('Updated edges after filter:', updatedEdges.map(e => e.id));
      saveEdges(updatedEdges);
      return { edges: updatedEdges };
    });
  },

  updateEdge: (edgeId, updates) => {
    set((state) => ({
      edges: state.edges.map(edge => 
        edge.id === edgeId ? { ...edge, ...updates } : edge
      )
    }));
  },

  // Helper functions
  getVisibleNodes: () => {
    const { currentUser, nodes } = get();
    if (!currentUser) return [];
    
    return nodes.filter(node => 
      node.permissions.viewRoles.includes(currentUser.role)
    );
  },

  getEditableNodes: () => {
    const { currentUser, nodes } = get();
    if (!currentUser) return [];
    
    return nodes.filter(node => 
      node.permissions.editRoles.includes(currentUser.role)
    );
  },

  canEditNode: (nodeId) => {
    const { currentUser, nodes } = get();
    if (!currentUser) return false;
    
    const node = nodes.find(n => n.id === nodeId);
    return node ? node.permissions.editRoles.includes(currentUser.role) : false;
  },

  canViewNode: (nodeId) => {
    const { currentUser, nodes } = get();
    if (!currentUser) return false;
    
    const node = nodes.find(n => n.id === nodeId);
    return node ? node.permissions.viewRoles.includes(currentUser.role) : false;
  }
}));