import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { StatisticsCharts } from '../components/StatisticsCharts';
import { useAppStore } from '../store/useAppStore';
import { 
  ArrowLeft, 
  Download, 
  Calendar, 
  Filter,
  TrendingUp,
  TrendingDown,
  Minus
} from 'lucide-react';

export const Statistics: React.FC = () => {
  const navigate = useNavigate();
  const { statistics, currentUser } = useAppStore();
  const [dateRange, setDateRange] = useState('7d');
  const [selectedMetric, setSelectedMetric] = useState('all');
  
  const handleExport = () => {
    // 导出统计数据的逻辑
    console.log('导出统计数据');
  };
  
  const handleBack = () => {
    navigate('/');
  };
  
  // 计算趋势
  const calculateTrend = (current: number, previous: number): string => {
    if (previous === 0) return '0';
    return ((current - previous) / previous * 100).toFixed(1);
  };
  
  // 模拟上期数据用于趋势计算
  const previousStats = {
    totalWaybills: 142,
    completedWaybills: 78,
    processingWaybills: 58,
    errorWaybills: 6,
    avgDeliveryTime: 31.2
  };
  
  const trends = {
    total: calculateTrend(statistics.totalWaybills, previousStats.totalWaybills),
    completed: calculateTrend(statistics.completedWaybills, previousStats.completedWaybills),
    processing: calculateTrend(statistics.processingWaybills, previousStats.processingWaybills),
    error: calculateTrend(statistics.errorWaybills, previousStats.errorWaybills),
    avgTime: calculateTrend(statistics.avgDeliveryTime, previousStats.avgDeliveryTime)
  };
  
  const getTrendIcon = (trend: string) => {
    const value = parseFloat(trend);
    return value >= 0 ? (
      <TrendingUp className="w-3 h-3 text-green-500" />
    ) : (
      <TrendingDown className="w-3 h-3 text-red-500" />
    );
  };
  
  const getTrendColor = (trend: string) => {
    const value = parseFloat(trend);
    return value >= 0 ? 'text-green-600' : 'text-red-600';
  };
  
  return (
    <div className="min-h-screen bg-gray-50">
      {/* 头部 */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={handleBack}
                className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <ArrowLeft className="w-5 h-5" />
              </button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">数据统计分析</h1>
                <p className="text-sm text-gray-600 mt-1">
                  物流运单节点数据统计与可视化分析
                </p>
              </div>
            </div>
            
            <div className="flex items-center space-x-3">
              {/* 时间范围选择 */}
              <div className="flex items-center space-x-2">
                <Calendar className="w-4 h-4 text-gray-500" />
                <select
                  value={dateRange}
                  onChange={(e) => setDateRange(e.target.value)}
                  className="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="1d">今日</option>
                  <option value="7d">近7天</option>
                  <option value="30d">近30天</option>
                  <option value="90d">近90天</option>
                </select>
              </div>
              
              {/* 指标筛选 */}
              <div className="flex items-center space-x-2">
                <Filter className="w-4 h-4 text-gray-500" />
                <select
                  value={selectedMetric}
                  onChange={(e) => setSelectedMetric(e.target.value)}
                  className="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="all">全部指标</option>
                  <option value="count">运单数量</option>
                  <option value="success">成功率</option>
                  <option value="time">处理时长</option>
                </select>
              </div>
              
              {/* 导出按钮 */}
              <button
                onClick={handleExport}
                className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <Download className="w-4 h-4" />
                <span>导出报告</span>
              </button>
            </div>
          </div>
        </div>
      </div>
      
      {/* 主要内容 */}
      <div className="max-w-7xl mx-auto px-6 py-6">
        {/* 关键指标卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-8">
          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <div className="flex items-center justify-between mb-2">
              <h3 className="text-sm font-medium text-gray-600">总运单数</h3>
              <div className="flex items-center space-x-1">
                {getTrendIcon(trends.total)}
                <span className={`text-xs font-medium ${getTrendColor(trends.total)}`}>
                  {trends.total}%
                </span>
              </div>
            </div>
            <div className="text-2xl font-bold text-gray-900">
              {statistics.totalWaybills}
            </div>
            <div className="text-xs text-gray-500 mt-1">
              较上期 {Math.abs(parseFloat(trends.total))}%
            </div>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <div className="flex items-center justify-between mb-2">
              <h3 className="text-sm font-medium text-gray-600">已完成</h3>
              <div className="flex items-center space-x-1">
                {getTrendIcon(trends.completed)}
                <span className={`text-xs font-medium ${getTrendColor(trends.completed)}`}>
                  {trends.completed}%
                </span>
              </div>
            </div>
            <div className="text-2xl font-bold text-green-600">
              {statistics.completedWaybills}
            </div>
            <div className="text-xs text-gray-500 mt-1">
              完成率 {((statistics.completedWaybills / statistics.totalWaybills) * 100).toFixed(1)}%
            </div>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <div className="flex items-center justify-between mb-2">
              <h3 className="text-sm font-medium text-gray-600">处理中</h3>
              <div className="flex items-center space-x-1">
                {getTrendIcon(trends.processing)}
                <span className={`text-xs font-medium ${getTrendColor(trends.processing)}`}>
                  {trends.processing}%
                </span>
              </div>
            </div>
            <div className="text-2xl font-bold text-blue-600">
              {statistics.processingWaybills}
            </div>
            <div className="text-xs text-gray-500 mt-1">
              占比 {((statistics.processingWaybills / statistics.totalWaybills) * 100).toFixed(1)}%
            </div>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <div className="flex items-center justify-between mb-2">
              <h3 className="text-sm font-medium text-gray-600">异常运单</h3>
              <div className="flex items-center space-x-1">
                {getTrendIcon(trends.error)}
                <span className={`text-xs font-medium ${getTrendColor(trends.error)}`}>
                  {trends.error}%
                </span>
              </div>
            </div>
            <div className="text-2xl font-bold text-red-600">
              {statistics.errorWaybills}
            </div>
            <div className="text-xs text-gray-500 mt-1">
              异常率 {((statistics.errorWaybills / statistics.totalWaybills) * 100).toFixed(1)}%
            </div>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <div className="flex items-center justify-between mb-2">
              <h3 className="text-sm font-medium text-gray-600">平均配送时长</h3>
              <div className="flex items-center space-x-1">
                {getTrendIcon(trends.avgTime)}
                <span className={`text-xs font-medium ${getTrendColor(trends.avgTime)}`}>
                  {trends.avgTime}%
                </span>
              </div>
            </div>
            <div className="text-2xl font-bold text-orange-600">
              {statistics.avgDeliveryTime}h
            </div>
            <div className="text-xs text-gray-500 mt-1">
              较上期 {Math.abs(parseFloat(trends.avgTime))}%
            </div>
          </div>
        </div>
        
        {/* 图表区域 */}
        <StatisticsCharts />
        
        {/* 详细数据表格 */}
        <div className="mt-8 bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">节点详细统计</h3>
          </div>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    节点名称
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    运单数量
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    成功率
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    平均处理时长
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    状态
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {statistics.nodeStats.map((stat, index) => (
                  <tr key={stat.nodeId} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{stat.nodeName}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{stat.count}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="text-sm text-gray-900">{stat.successRate}%</div>
                        <div className="ml-2 w-16 bg-gray-200 rounded-full h-2">
                          <div 
                            className="bg-green-500 h-2 rounded-full" 
                            style={{ width: `${stat.successRate}%` }}
                          />
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{stat.avgProcessTime}小时</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        stat.successRate >= 95 ? 'bg-green-100 text-green-800' :
                        stat.successRate >= 85 ? 'bg-yellow-100 text-yellow-800' :
                        'bg-red-100 text-red-800'
                      }`}>
                        {stat.successRate >= 95 ? '优秀' :
                         stat.successRate >= 85 ? '良好' : '需改进'}
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
};