import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { ReactFlowProvider } from 'reactflow';
import { FlowChart } from '../components/FlowChart';
import { NodeDetailPanel } from '../components/NodeDetailPanel';
import RoleManagement from '../components/RoleManagement';
import { useAppStore } from '../store/useAppStore';
import { 
  Edit3, 
  Eye, 
  BarChart3, 
  Users, 
  Settings,
  Search,
  Filter,
  RefreshCw,
  UserCheck,
  User,
  Edit,
  X
} from 'lucide-react';

export const FlowDashboard: React.FC = () => {
  const { 
    currentUser, 
    isEditMode, 
    toggleEditMode, 
    selectedNodeId,
    selectNode,
    refreshStatistics
  } = useAppStore();
  
  const [isPanelOpen, setIsPanelOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [isRolePanelOpen, setIsRolePanelOpen] = useState(false);
  
  const handleNodeSelect = (nodeId: string) => {
    selectNode(nodeId);
    setIsPanelOpen(true);
  };
  
  const handleRefresh = () => {
    refreshStatistics();
    // 这里可以添加刷新节点数据的逻辑
  };
  
  return (
    <div className="flex h-screen bg-gray-50">
      {/* 主要内容区域 */}
      <div className="flex-1 flex flex-col">
        {/* 顶部工具栏 */}
        <div className="bg-white border-b border-gray-200 px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <h1 className="text-2xl font-bold text-gray-900">物流运单节点可视化</h1>
            </div>
            
            <div className="flex items-center space-x-4">
              {/* 用户角色指示器 */}
              <div className="flex items-center space-x-2 px-3 py-2 bg-gray-100 rounded-lg">
                <User className="w-4 h-4 text-gray-600" />
                <span className="text-sm font-medium text-gray-700">
                  {currentUser?.name} ({currentUser?.role === 'admin' ? '管理员' : 
                   currentUser?.role === 'dispatcher' ? '调度员' : '客户'})
                </span>
              </div>
              
              {/* 编辑模式切换 */}
              {currentUser?.role === 'admin' && (
                <button
                  onClick={toggleEditMode}
                  className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
                    isEditMode 
                      ? 'bg-orange-100 text-orange-700 hover:bg-orange-200' 
                      : 'bg-blue-100 text-blue-700 hover:bg-blue-200'
                  }`}
                >
                  <Edit className="w-4 h-4" />
                  <span>{isEditMode ? '退出编辑' : '编辑模式'}</span>
                </button>
              )}
              
              {/* 刷新按钮 */}
              <button
                onClick={handleRefresh}
                className="flex items-center space-x-2 px-4 py-2 bg-green-100 text-green-700 hover:bg-green-200 rounded-lg transition-colors"
              >
                <RefreshCw className="w-4 h-4" />
                <span>刷新</span>
              </button>
              
              {/* 角色权限管理页面入口 */}
              <Link
                to="/roles"
                className="flex items-center space-x-2 px-4 py-2 bg-indigo-100 text-indigo-700 hover:bg-indigo-200 rounded-lg transition-colors"
              >
                <Users className="w-4 h-4" />
                <span>角色权限管理</span>
              </Link>
              
              {/* 角色切换按钮 */}
              <button
                onClick={() => setIsPanelOpen(!isPanelOpen)}
                className="flex items-center space-x-2 px-4 py-2 bg-purple-100 text-purple-700 hover:bg-purple-200 rounded-lg transition-colors"
              >
                <Settings className="w-4 h-4" />
                <span>角色切换</span>
              </button>
            </div>
          </div>
        </div>
        
        {/* 流程图区域 */}
        <div className="flex-1 relative">
          <ReactFlowProvider>
            <FlowChart className="w-full h-full" />
          </ReactFlowProvider>
        </div>
      </div>
      
      {/* 侧边面板 */}
      {isPanelOpen && (
        <div className="w-80 bg-white border-l border-gray-200 shadow-lg">
          <div className="p-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-900">角色管理</h3>
              <button
                onClick={() => setIsPanelOpen(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
          </div>
          
          <div className="p-4">
            <RoleManagement />
          </div>
        </div>
      )}
    </div>
  );
};