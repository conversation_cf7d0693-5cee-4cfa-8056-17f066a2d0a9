import React, { useCallback, useMemo, useState } from 'react';
import React<PERSON>low, {
  Node,
  Edge,
  addEdge,
  Connection,
  useNodesState,
  useEdgesState,
  Controls,
  MiniMap,
  Background,
  BackgroundVariant,
  ConnectionMode,
  useReactFlow
} from 'reactflow';
import 'reactflow/dist/style.css';
import { toast } from 'sonner';
import { useAppStore } from '../store/useAppStore';
import { nodeTypes } from './FlowNode';
import { FlowNode as FlowNodeType } from '../types';
import { RotateCcw, Plus, Trash2, Link, Unlink } from 'lucide-react';

interface FlowChartProps {
  className?: string;
}

export const FlowChart: React.FC<FlowChartProps> = ({ className = '' }) => {
  const { 
    nodes: storeNodes, 
    edges: storeEdges, 
    getVisibleNodes, 
    isEditMode,
    currentUser,
    updateNodePosition,
    resetNodePositions,
    addNode,
    deleteNode,
    addEdge: storeAddEdge,
    deleteEdge
  } = useAppStore();
  
  const [isAddingNode, setIsAddingNode] = useState(false);
  const [selectedNodes, setSelectedNodes] = useState<string[]>([]);
  const [selectedEdges, setSelectedEdges] = useState<string[]>([]);
  const [isConnectingMode, setIsConnectingMode] = useState(false);
  const [draggedNode, setDraggedNode] = useState<any>(null);
  const [isDragging, setIsDragging] = useState(false);
  const reactFlowInstance = useReactFlow();

  // 获取当前用户可见的节点
  const visibleNodes = useMemo(() => getVisibleNodes(), [getVisibleNodes]);
  
  // 转换为ReactFlow格式的节点
  const reactFlowNodes: Node[] = useMemo(() => {
    return visibleNodes.map((node: FlowNodeType) => ({
      id: node.id,
      type: 'flowNode',
      position: node.position,
      data: node,
      draggable: isEditMode,
      selectable: true,
      deletable: isEditMode,
      selected: selectedNodes.includes(node.id)
    }));
  }, [visibleNodes, isEditMode, selectedNodes]);

  // 过滤边，只显示可见节点之间的连接
  const visibleEdges = useMemo(() => {
    const visibleNodeIds = new Set(visibleNodes.map(n => n.id));
    return storeEdges.filter(edge => 
      visibleNodeIds.has(edge.source) && visibleNodeIds.has(edge.target)
    );
  }, [storeEdges, visibleNodes]);

  const [nodes, setNodes, onNodesChange] = useNodesState(reactFlowNodes);
  const [edges, setEdges, defaultOnEdgesChange] = useEdgesState(visibleEdges.map(edge => {
    return {
      ...edge,
      selected: selectedEdges.includes(edge.id),
      selectable: true,
      deletable: isEditMode,
      style: {
        ...edge.style,
        strokeWidth: selectedEdges.includes(edge.id) ? 3 : 2,
        stroke: selectedEdges.includes(edge.id) ? '#EF4444' : (edge.style?.stroke || '#6B7280')
      }
    };
  }));

  // 自定义边变化处理函数，同步删除操作到store
  const onEdgesChange = useCallback((changes: any[]) => {
    console.log('onEdgesChange called with:', changes);

    // 处理删除操作
    const removeChanges = changes.filter(change => change.type === 'remove');
    if (removeChanges.length > 0 && isEditMode) {
      console.log('Processing edge removals:', removeChanges);
      removeChanges.forEach(change => {
        console.log('Removing edge from store:', change.id);
        deleteEdge(change.id);
      });

      // 清除选中状态
      const removedIds = removeChanges.map(change => change.id);
      setSelectedEdges(prev => prev.filter(id => !removedIds.includes(id)));

      toast.success(`已删除 ${removeChanges.length} 条连线`, {
        duration: 2000
      });
    }

    // 应用默认的边变化处理
    defaultOnEdgesChange(changes);
  }, [defaultOnEdgesChange, deleteEdge, isEditMode, setSelectedEdges]);
  const [isInitialized, setIsInitialized] = React.useState(false);

  // 只在初始化时设置节点，避免覆盖用户拖拽的位置
  React.useEffect(() => {
    if (!isInitialized) {
      setNodes(reactFlowNodes);
      setIsInitialized(true);
    } else {
      // 只更新节点数据，保持位置不变
      setNodes(currentNodes => 
        currentNodes.map(currentNode => {
          const updatedNode = reactFlowNodes.find(n => n.id === currentNode.id);
          return updatedNode ? {
            ...updatedNode,
            position: currentNode.position // 保持当前位置
          } : currentNode;
        })
      );
    }
  }, [reactFlowNodes, setNodes, isInitialized]);

  // 当可见节点发生变化时（角色切换），重新初始化
  React.useEffect(() => {
    setNodes(reactFlowNodes);
  }, [visibleNodes.length, setNodes]);

  React.useEffect(() => {
    console.log('Updating edges effect:', {
      visibleEdgesCount: visibleEdges.length,
      selectedEdgesCount: selectedEdges.length,
      visibleEdges: visibleEdges.map(e => e.id),
      selectedEdges
    });

    setEdges(currentEdges => {
      // 检查是否需要更新
      const currentEdgeIds = new Set(currentEdges.map(e => e.id));
      const visibleEdgeIds = new Set(visibleEdges.map(e => e.id));

      // 如果边的数量和ID都相同，只更新选择状态和样式
      const needsFullUpdate = currentEdgeIds.size !== visibleEdgeIds.size ||
        !Array.from(visibleEdgeIds).every(id => currentEdgeIds.has(id));

      if (needsFullUpdate) {
        console.log('Full edge update needed');
        const updatedEdges = visibleEdges.map(edge => ({
          ...edge,
          selected: selectedEdges.includes(edge.id),
          selectable: true,
          deletable: isEditMode,
          style: {
            ...edge.style,
            strokeWidth: selectedEdges.includes(edge.id) ? 3 : 2,
            stroke: selectedEdges.includes(edge.id) ? '#EF4444' : (edge.style?.stroke || '#6B7280')
          }
        }));

        console.log('Updated edges:', updatedEdges.map(e => ({ id: e.id, selected: e.selected, selectable: e.selectable })));
        return updatedEdges;
      } else {
        // 只更新选择状态和样式
        console.log('Updating edge selection and styles only');
        return currentEdges.map(edge => ({
          ...edge,
          selected: selectedEdges.includes(edge.id),
          selectable: true,
          deletable: isEditMode,
          style: {
            ...edge.style,
            strokeWidth: selectedEdges.includes(edge.id) ? 3 : 2,
            stroke: selectedEdges.includes(edge.id) ? '#EF4444' : (edge.style?.stroke || '#6B7280')
          }
        }));
      }
    });
  }, [visibleEdges, selectedEdges, setEdges, isEditMode]);

  const onConnect = useCallback(
    (params: Connection) => {
      if (isEditMode && params.source && params.target) {
        // 添加到本地状态
        setEdges((eds) => addEdge({
          ...params,
          type: 'smoothstep',
          animated: true,
          style: { stroke: '#6B7280', strokeWidth: 2 }
        }, eds));
        
        // 添加到store
        storeAddEdge({
          source: params.source,
          target: params.target,
          type: 'smoothstep',
          animated: true,
          style: { stroke: '#6B7280', strokeWidth: 2 }
        });
        
        toast.success('连线已添加', {
          description: `已在节点 ${params.source} 和 ${params.target} 之间添加连线`,
          duration: 2000
        });
      }
    },
    [setEdges, isEditMode, storeAddEdge]
  );

  // 节点拖拽结束时更新store中的位置
  const onNodeDragStop = useCallback(
    (event: React.MouseEvent, node: Node) => {
      if (isEditMode) {
        // 更新store中节点位置
        updateNodePosition(node.id, node.position);
        toast.success('节点位置已保存到本地缓存', {
          description: `节点 "${node.data?.name || node.id}" 的位置已保存，刷新页面后会自动恢复`,
          duration: 3000
        });
        console.log('Node position saved to localStorage:', node.id, node.position);
      }
    },
    [isEditMode, updateNodePosition]
  );

  // 节点和边选择变化
  const onSelectionChange = useCallback(
    ({ nodes: selectedNodes, edges: selectedEdges }: { nodes: Node[], edges: Edge[] }) => {
      console.log('Selection changed:', {
        isEditMode,
        selectedNodes: selectedNodes.map(n => n.id),
        selectedEdges: selectedEdges.map(e => e.id),
        totalEdges: edges.length
      });
      
      if (isEditMode) {
        setSelectedNodes(selectedNodes.map(node => node.id));
        setSelectedEdges(selectedEdges.map(edge => edge.id));
        
        console.log('Updated selection state:', {
          selectedNodeIds: selectedNodes.map(node => node.id),
          selectedEdgeIds: selectedEdges.map(edge => edge.id)
        });
      }
    },
    [isEditMode, edges.length]
  );

  // 删除选中的节点
  const handleDeleteSelectedNodes = useCallback(() => {
    if (isEditMode && selectedNodes.length > 0) {
      selectedNodes.forEach(nodeId => {
        deleteNode(nodeId);
      });
      setSelectedNodes([]);
      toast.success(`已删除 ${selectedNodes.length} 个节点`, {
        description: '相关连线也已自动删除',
        duration: 2000
      });
    }
  }, [isEditMode, selectedNodes, deleteNode]);

  // 删除选中的连线
  const handleDeleteSelectedEdges = useCallback(() => {
    console.log('handleDeleteSelectedEdges called:', {
      isEditMode,
      selectedEdgesLength: selectedEdges.length,
      selectedEdges,
      currentEdges: edges.map(e => ({ id: e.id, selected: e.selected }))
    });

    if (isEditMode && selectedEdges.length > 0) {
      console.log('Starting edge deletion process for edges:', selectedEdges);

      // 从store删除（这会触发visibleEdges的更新，进而更新本地edges状态）
      selectedEdges.forEach(edgeId => {
        console.log('Calling deleteEdge for:', edgeId);
        deleteEdge(edgeId);
      });

      setSelectedEdges([]);
      console.log('Edge deletion completed, cleared selection');

      toast.success(`已删除 ${selectedEdges.length} 条连线`, {
        duration: 2000
      });
    } else {
      console.log('Edge deletion skipped:', {
        reason: !isEditMode ? 'Not in edit mode' : 'No edges selected',
        isEditMode,
        selectedEdgesLength: selectedEdges.length
      });
    }
  }, [isEditMode, selectedEdges, deleteEdge]);

  // 开始拖拽添加节点
  const handleStartAddNode = useCallback(() => {
    if (isEditMode) {
      setIsAddingNode(true);
      toast.info('拖拽节点到画布上添加', {
        description: '将节点拖拽到想要的位置',
        duration: 3000
      });
    }
  }, [isEditMode]);

  // 拖拽开始
  const onDragStart = useCallback((event: React.DragEvent, nodeType: string) => {
    setIsDragging(true);
    event.dataTransfer.setData('application/reactflow', nodeType);
    event.dataTransfer.effectAllowed = 'move';
  }, []);

  // 拖拽结束
  const onDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = 'move';
  }, []);

  // 放置节点
  const onDrop = useCallback(
    (event: React.DragEvent) => {
      event.preventDefault();
      setIsDragging(false);
      setIsAddingNode(false);

      const reactFlowBounds = event.currentTarget.getBoundingClientRect();
      const type = event.dataTransfer.getData('application/reactflow');

      if (typeof type === 'undefined' || !type) {
        return;
      }

      // 检查reactFlowInstance是否已初始化
      if (!reactFlowInstance) {
        console.error('ReactFlow instance not initialized');
        toast.error('画布未初始化，请稍后再试');
        return;
      }

      const position = reactFlowInstance.project({
        x: event.clientX - reactFlowBounds.left,
        y: event.clientY - reactFlowBounds.top,
      });

      const newNodeId = addNode({
        type: 'custom',
        name: '新节点',
        description: '自定义节点',
        status: 'processing',
        position,
        permissions: {
          viewRoles: ['admin', 'dispatcher'],
          editRoles: ['admin']
        },
        style: {
          backgroundColor: '#1E3A8A',
          borderColor: '#1E40AF',
          textColor: '#FFFFFF'
        },
        data: {
          count: 10
        }
      });

      // 立即更新本地节点状态
      const newNode = {
        id: newNodeId,
        type: 'flowNode' as const,
        position,
        data: {
          id: newNodeId,
          type: 'custom' as const,
          name: '新节点',
          description: '自定义节点',
          status: 'processing' as const,
          position,
          permissions: {
            viewRoles: ['admin', 'dispatcher'],
            editRoles: ['admin']
          },
          style: {
            backgroundColor: '#1E3A8A',
            borderColor: '#1E40AF',
            textColor: '#FFFFFF'
          },
          data: {
             count: 10
           }
        },
        draggable: true,
        selectable: true,
        deletable: true
      };
      
      setNodes(currentNodes => {
        const updatedNodes = [...currentNodes, newNode];
        console.log('Added new node to local state:', newNodeId, updatedNodes.length);
        return updatedNodes;
      });

      toast.success('新节点已添加', {
        description: '可以编辑节点名称和设置阈值',
        duration: 2000
      });
    },
    [reactFlowInstance, addNode, setNodes]
  );

  // 双击画布添加节点（保留原功能）
  const onPaneDoubleClick = useCallback(
    (event: React.MouseEvent) => {
      if (isEditMode && isAddingNode) {
        // 检查reactFlowInstance是否已初始化
        if (!reactFlowInstance) {
          console.error('ReactFlow instance not initialized');
          toast.error('画布未初始化，请稍后再试');
          return;
        }

        const reactFlowBounds = event.currentTarget.getBoundingClientRect();
        const position = reactFlowInstance.project({
          x: event.clientX - reactFlowBounds.left,
          y: event.clientY - reactFlowBounds.top
        });

        const newNodeId = addNode({
          type: 'custom',
          name: '新节点',
          description: '自定义节点',
          status: 'processing',
          position,
          permissions: {
            viewRoles: ['admin', 'dispatcher'],
            editRoles: ['admin']
          },
          style: {
            backgroundColor: '#1E3A8A',
            borderColor: '#1E40AF',
            textColor: '#FFFFFF'
          },
          data: {
            count: 10
          }
        });

        // 立即更新本地节点状态
        const newNode = {
          id: newNodeId,
          type: 'flowNode' as const,
          position,
          data: {
            id: newNodeId,
            type: 'custom' as const,
            name: '新节点',
            description: '自定义节点',
            status: 'processing' as const,
            position,
            permissions: {
              viewRoles: ['admin', 'dispatcher'],
              editRoles: ['admin']
            },
            style: {
              backgroundColor: '#1E3A8A',
              borderColor: '#1E40AF',
              textColor: '#FFFFFF'
            },
            data: {
               count: 10
             }
          },
          draggable: true,
          selectable: true,
          deletable: true
        };
        
        setNodes(currentNodes => {
          const updatedNodes = [...currentNodes, newNode];
          console.log('Added new node via double-click:', newNodeId, updatedNodes.length);
          return updatedNodes;
        });

        setIsAddingNode(false);
        toast.success('新节点已添加', {
          description: '拖拽节点可以移动位置',
          duration: 2000
        });
      }
    },
    [isEditMode, isAddingNode, addNode, reactFlowInstance]
  );

  // 键盘事件处理
  const onKeyDown = useCallback(
    (event: KeyboardEvent) => {
      if (isEditMode && event.key === 'Delete') {
        if (selectedNodes.length > 0) {
          handleDeleteSelectedNodes();
        } else if (selectedEdges.length > 0) {
          handleDeleteSelectedEdges();
        }
      }
    },
    [isEditMode, selectedNodes, selectedEdges, handleDeleteSelectedNodes, handleDeleteSelectedEdges]
  );

  // 监听键盘事件
  React.useEffect(() => {
    document.addEventListener('keydown', onKeyDown);
    return () => {
      document.removeEventListener('keydown', onKeyDown);
    };
  }, [onKeyDown]);

  const proOptions = {
    hideAttribution: true
  };

  return (
    <div className={`w-full h-full bg-gray-50 ${className}`}>
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
        onNodeDragStop={onNodeDragStop}
        onSelectionChange={onSelectionChange}
        onDoubleClick={onPaneDoubleClick}
        onDrop={onDrop}
        onDragOver={onDragOver}
        nodeTypes={nodeTypes}
        connectionMode={ConnectionMode.Loose}
        fitView
        fitViewOptions={{
          padding: 0.5,
          includeHiddenNodes: false,
          minZoom: 0.3,
          maxZoom: 2.0
        }}
        proOptions={proOptions}
        className="bg-gray-50"
        multiSelectionKeyCode="Shift"
        deleteKeyCode="Delete"
      >
        <Controls 
          className="bg-white shadow-lg border border-gray-200 rounded-lg"
          showZoom={true}
          showFitView={true}
          showInteractive={false}
        />
        
        <MiniMap 
          className="bg-white shadow-lg border border-gray-200 rounded-lg"
          nodeColor={(node) => {
            const nodeData = node.data as FlowNodeType;
            return nodeData.style?.backgroundColor || '#3B82F6';
          }}
          nodeStrokeWidth={2}
          zoomable
          pannable
        />
        
        <Background 
          variant={BackgroundVariant.Dots} 
          gap={20} 
          size={1} 
          color="#E5E7EB"
        />
        
        {/* 节点状态信息 - 右上角横向展示 */}
        <div className="absolute top-4 right-4 bg-white shadow-lg rounded-lg p-3 border border-gray-200">
          <div className="flex items-center space-x-4 text-xs">
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-green-500 rounded" />
              <span>已完成</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-blue-500 rounded" />
              <span>处理中</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-orange-500 rounded" />
              <span>警告</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-red-500 rounded" />
              <span>异常</span>
            </div>
          </div>
        </div>
        
        {/* 用户角色和编辑模式指示器 */}
        <div className="absolute top-4 left-4 bg-white shadow-lg rounded-lg p-3 border border-gray-200">
          <div className="flex items-center justify-between space-x-3">
            <div className="flex items-center space-x-2 text-sm">
              <div className="flex items-center space-x-1">
                <div className={`w-2 h-2 rounded-full ${
                  currentUser?.role === 'admin' ? 'bg-red-500' :
                  currentUser?.role === 'dispatcher' ? 'bg-blue-500' : 'bg-green-500'
                }`} />
                <span className="font-medium">
                  {currentUser?.role === 'admin' ? '管理员' :
                   currentUser?.role === 'dispatcher' ? '调度员' : '客户'}
                </span>
              </div>
              {isEditMode && (
                <div className="flex items-center space-x-1 text-orange-600">
                  <div className="w-2 h-2 bg-orange-500 rounded-full animate-pulse" />
                  <span className="text-xs">编辑模式</span>
                </div>
              )}
            </div>
            {/* 编辑模式控制按钮 */}
            {isEditMode && (
              <div className="flex items-center space-x-1">
                <button
                  onClick={handleStartAddNode}
                  className={`flex items-center space-x-1 px-2 py-1 text-xs rounded transition-colors ${
                    isAddingNode 
                      ? 'bg-green-100 text-green-700 hover:bg-green-200' 
                      : 'bg-blue-100 text-blue-700 hover:bg-blue-200'
                  }`}
                  title="点击后拖拽节点到画布上"
                >
                  <Plus className="w-3 h-3" />
                  <span>{isAddingNode ? '拖拽节点' : '添加节点'}</span>
                </button>
                
                {selectedNodes.length > 0 && (
                  <button
                    onClick={handleDeleteSelectedNodes}
                    className="flex items-center space-x-1 px-2 py-1 text-xs bg-red-100 text-red-700 hover:bg-red-200 rounded transition-colors"
                    title="删除选中的节点"
                  >
                    <Trash2 className="w-3 h-3" />
                    <span>删除节点({selectedNodes.length})</span>
                  </button>
                )}
                
                {selectedEdges.length > 0 && (
                  <button
                    onClick={handleDeleteSelectedEdges}
                    className="flex items-center space-x-1 px-2 py-1 text-xs bg-red-100 text-red-700 hover:bg-red-200 rounded transition-colors"
                    title="删除选中的连线"
                  >
                    <Unlink className="w-3 h-3" />
                    <span>删除连线({selectedEdges.length})</span>
                  </button>
                )}
                
                <button
                  onClick={() => {
                    resetNodePositions();
                    toast.success('节点位置已重置', {
                      description: '所有节点已恢复到默认位置，本地缓存已清除',
                      duration: 3000
                    });
                  }}
                  className="flex items-center space-x-1 px-2 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded transition-colors"
                  title="重置所有节点到默认位置"
                >
                  <RotateCcw className="w-3 h-3" />
                  <span>重置位置</span>
                </button>
              </div>
            )}
          </div>
        </div>
        
        {/* 可拖拽的节点模板 */}
        {isEditMode && isAddingNode && (
          <div className="absolute bottom-4 right-4 bg-white shadow-lg rounded-lg p-4 border border-gray-200">
            <div className="text-xs font-medium text-gray-700 mb-3">拖拽节点到画布</div>
            <div 
              className="w-24 h-16 bg-blue-600 rounded-lg shadow-lg cursor-grab active:cursor-grabbing flex flex-col items-center justify-center text-white text-xs font-medium border-2 border-blue-700 hover:bg-blue-700 transition-colors"
              draggable
              onDragStart={(event) => onDragStart(event, 'custom')}
            >
              <div className="text-center">
                <div className="font-bold">新节点</div>
                <div className="text-xs opacity-80">运单: 10</div>
              </div>
            </div>
            <div className="text-xs text-gray-500 mt-2 text-center">
              拖拽到画布上添加
            </div>
            <button
              onClick={() => setIsAddingNode(false)}
              className="mt-2 w-full text-xs text-gray-500 hover:text-gray-700 transition-colors"
            >
              取消添加
            </button>
          </div>
        )}
        
        {/* 操作说明 */}
        {isEditMode && !isAddingNode && (
          <div className="absolute bottom-4 left-4 bg-white shadow-lg rounded-lg p-3 border border-gray-200">
            <div className="text-xs font-medium text-gray-700 mb-2">编辑操作</div>
            <div className="space-y-1 text-xs text-gray-600">
              <div>• 拖拽节点移动位置</div>
              <div>• 点击"添加节点"后拖拽到画布</div>
              <div>• 拖拽节点边缘创建连线</div>
              <div>• 选中节点/连线后按Delete删除</div>
              <div>• 按住Shift多选节点</div>
              <div>• 双击节点编辑名称和阈值</div>
            </div>
          </div>
        )}
      </ReactFlow>
    </div>
  );
};