import React from 'react';
import { Users, Shield, User } from 'lucide-react';
import { useAppStore } from '../store/useAppStore';

const RoleManagement: React.FC = () => {
  const { currentUser, switchRole } = useAppStore();

  const roles = [
    {
      id: 'admin',
      name: '管理员',
      description: '拥有所有权限，可查看和编辑所有节点',
      icon: Shield,
      color: 'bg-red-100 text-red-800 border-red-200'
    },
    {
      id: 'dispatcher',
      name: '调度员',
      description: '可查看大部分节点，编辑部分节点',
      icon: Users,
      color: 'bg-blue-100 text-blue-800 border-blue-200'
    },
    {
      id: 'customer',
      name: '客户',
      description: '只能查看有限的节点信息',
      icon: User,
      color: 'bg-green-100 text-green-800 border-green-200'
    }
  ];

  const handleRoleSwitch = (roleId: string) => {
    switchRole(roleId as 'admin' | 'dispatcher' | 'customer');
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="mb-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-2">角色管理</h2>
        <p className="text-sm text-gray-600">
          当前角色: <span className="font-medium">{currentUser?.name}</span> ({currentUser?.role === 'admin' ? '管理员' : currentUser?.role === 'dispatcher' ? '调度员' : '客户'})
        </p>
      </div>

      <div className="space-y-4">
        <h3 className="text-sm font-medium text-gray-900">切换角色</h3>
        
        <div className="grid gap-3">
          {roles.map((role) => {
            const IconComponent = role.icon;
            const isCurrentRole = currentUser?.role === role.id;
            
            return (
              <button
                key={role.id}
                onClick={() => handleRoleSwitch(role.id)}
                disabled={isCurrentRole}
                className={`
                  w-full p-4 rounded-lg border-2 transition-all duration-200 text-left
                  ${
                    isCurrentRole
                      ? `${role.color} border-current cursor-default`
                      : 'bg-gray-50 text-gray-700 border-gray-200 hover:bg-gray-100 hover:border-gray-300 cursor-pointer'
                  }
                `}
              >
                <div className="flex items-start space-x-3">
                  <div className={`
                    p-2 rounded-lg
                    ${
                      isCurrentRole
                        ? 'bg-white bg-opacity-50'
                        : 'bg-white'
                    }
                  `}>
                    <IconComponent className="w-5 h-5" />
                  </div>
                  
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <h4 className="font-medium">{role.name}</h4>
                      {isCurrentRole && (
                        <span className="px-2 py-1 bg-white bg-opacity-50 rounded-full text-xs font-medium">
                          当前
                        </span>
                      )}
                    </div>
                    <p className={`
                      text-sm mt-1
                      ${
                        isCurrentRole
                          ? 'opacity-90'
                          : 'text-gray-600'
                      }
                    `}>
                      {role.description}
                    </p>
                  </div>
                </div>
              </button>
            );
          })}
        </div>
      </div>

      <div className="mt-6 p-4 bg-blue-50 rounded-lg">
        <div className="flex items-start space-x-2">
          <div className="w-5 h-5 text-blue-600 mt-0.5">
            <svg viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
            </svg>
          </div>
          <div>
            <h4 className="text-sm font-medium text-blue-900 mb-1">提示</h4>
            <p className="text-sm text-blue-800">
              切换角色后，流程图中的节点可见性和编辑权限会相应改变。不同角色可以看到和操作的节点范围不同。
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RoleManagement;