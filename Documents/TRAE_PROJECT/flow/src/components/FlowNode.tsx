import React, { useState, useRef, useEffect } from 'react';
import { <PERSON><PERSON>, Position, NodeProps } from 'reactflow';
import { FlowNode as FlowNodeType } from '../types';
import { useAppStore } from '../store/useAppStore';
import { 
  Play, 
  Truck, 
  Navigation, 
  AlertTriangle, 
  Package, 
  CheckCircle,
  Clock,
  Users,
  TrendingUp,
  Edit3,
  Check,
  X
} from 'lucide-react';

const getNodeIcon = (type: FlowNodeType['type']) => {
  switch (type) {
    case 'start': return <Play className="w-4 h-4" />;
    case 'departure': return <Truck className="w-4 h-4" />;
    case 'transit': return <Navigation className="w-4 h-4" />;
    case 'special': return <AlertTriangle className="w-4 h-4" />;
    case 'unloading': return <Package className="w-4 h-4" />;
    case 'completed': return <CheckCircle className="w-4 h-4" />;
    default: return <Play className="w-4 h-4" />;
  }
};

const getStatusColor = (status: FlowNodeType['status']) => {
  switch (status) {
    case 'completed': return 'bg-green-500 border-green-600';
    case 'processing': return 'bg-blue-500 border-blue-600';
    case 'warning': return 'bg-orange-500 border-orange-600';
    case 'error': return 'bg-red-500 border-red-600';
    case 'pending': return 'bg-gray-500 border-gray-600';
    default: return 'bg-gray-500 border-gray-600';
  }
};

export const FlowNode: React.FC<NodeProps<FlowNodeType>> = ({ data, selected }) => {
  const { selectNode, selectedNodeId, canEditNode, isEditMode, updateNode } = useAppStore();
  const [isEditing, setIsEditing] = useState(false);
  const [editName, setEditName] = useState(data.name);
  const [editThreshold, setEditThreshold] = useState(data.data.count);
  const nameInputRef = useRef<HTMLInputElement>(null);
  const thresholdInputRef = useRef<HTMLInputElement>(null);
  
  const isSelected = selectedNodeId === data.id;
  const canEdit = canEditNode(data.id);

  const handleClick = () => {
    selectNode(data.id);
  };

  const handleDoubleClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (canEdit && isEditMode) {
      setIsEditing(true);
      setEditName(data.name);
      setEditThreshold(data.data.count);
    }
  };

  const handleSaveEdit = () => {
    updateNode(data.id, {
      name: editName,
      data: {
        ...data.data,
        count: editThreshold
      }
    });
    setIsEditing(false);
  };

  const handleCancelEdit = () => {
    setEditName(data.name);
    setEditThreshold(data.data.count);
    setIsEditing(false);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSaveEdit();
    } else if (e.key === 'Escape') {
      handleCancelEdit();
    }
  };

  useEffect(() => {
    if (isEditing && nameInputRef.current) {
      nameInputRef.current.focus();
      nameInputRef.current.select();
    }
  }, [isEditing]);

  const statusColorClass = data.style?.backgroundColor 
    ? '' 
    : getStatusColor(data.status);

  const nodeStyle = data.style ? {
    backgroundColor: data.style.backgroundColor,
    borderColor: data.style.borderColor,
    color: data.style.textColor
  } : {};

  return (
    <div 
      className={`
        relative min-w-[180px] p-4 rounded-lg border-2 shadow-lg cursor-pointer transition-all duration-200
        ${statusColorClass}
        ${isSelected ? 'ring-4 ring-blue-300 ring-opacity-50' : ''}
        ${canEdit && isEditMode ? 'hover:shadow-xl hover:scale-105' : 'hover:shadow-md'}
        ${isEditing ? 'ring-4 ring-yellow-300 ring-opacity-50' : ''}
        text-white
      `}
      style={nodeStyle}
      onClick={handleClick}
      onDoubleClick={handleDoubleClick}
    >
      {/* 输入连接点 */}
      <Handle
        type="target"
        position={Position.Left}
        className="w-3 h-3 bg-white border-2 border-gray-400"
      />
      
      {/* 节点头部 */}
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center space-x-2 flex-1">
          {getNodeIcon(data.type)}
          {isEditing ? (
            <input
              ref={nameInputRef}
              type="text"
              value={editName}
              onChange={(e) => setEditName(e.target.value)}
              onKeyDown={handleKeyDown}
              className="bg-white text-black px-2 py-1 rounded text-sm font-semibold min-w-0 flex-1"
              onClick={(e) => e.stopPropagation()}
            />
          ) : (
            <span className="font-semibold text-sm">{data.name}</span>
          )}
        </div>
        {isEditing ? (
          <div className="flex items-center space-x-1 ml-2">
            <button
              onClick={(e) => {
                e.stopPropagation();
                handleSaveEdit();
              }}
              className="w-6 h-6 bg-green-500 hover:bg-green-600 rounded flex items-center justify-center transition-colors"
            >
              <Check className="w-3 h-3" />
            </button>
            <button
              onClick={(e) => {
                e.stopPropagation();
                handleCancelEdit();
              }}
              className="w-6 h-6 bg-red-500 hover:bg-red-600 rounded flex items-center justify-center transition-colors"
            >
              <X className="w-3 h-3" />
            </button>
          </div>
        ) : (
          <div className="flex items-center space-x-1">
            {canEdit && isEditMode && (
              <Edit3 className="w-3 h-3 opacity-60" />
            )}
            {canEdit && isEditMode && (
              <div className="w-2 h-2 bg-yellow-400 rounded-full animate-pulse" />
            )}
          </div>
        )}
      </div>
      
      {/* 统计数据 */}
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <span className="flex items-center space-x-1 text-sm">
            <Users className="w-4 h-4" />
            <span>运单数</span>
          </span>
          {isEditing ? (
            <input
              ref={thresholdInputRef}
              type="number"
              value={editThreshold}
              onChange={(e) => setEditThreshold(parseInt(e.target.value) || 0)}
              onKeyDown={handleKeyDown}
              className="bg-white text-black px-2 py-1 rounded text-xl font-bold w-16 text-center"
              onClick={(e) => e.stopPropagation()}
            />
          ) : (
            <span className="font-bold text-2xl">{data.data.count}</span>
          )}
        </div>
        {isEditing && (
          <div className="text-xs opacity-80 text-center mt-2">
            双击编辑 | Enter保存 | Esc取消
          </div>
        )}
      </div>
      
      {/* 输出连接点 */}
      <Handle
        type="source"
        position={Position.Right}
        className="w-3 h-3 bg-white border-2 border-gray-400"
      />
    </div>
  );
};

// 注册自定义节点类型
export const nodeTypes = {
  flowNode: FlowNode
};