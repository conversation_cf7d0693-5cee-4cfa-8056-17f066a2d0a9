import React, { useState, useEffect } from 'react';
import { useAppStore } from '../store/useAppStore';
import { FlowNode, NodeEditForm } from '../types';
import { 
  X, 
  Edit3, 
  Save, 
  Clock, 
  Users, 
  TrendingUp, 
  AlertTriangle,
  Eye,
  EyeOff,
  Package,
  CheckCircle
} from 'lucide-react';

interface NodeDetailPanelProps {
  isOpen: boolean;
  onClose: () => void;
}

export const NodeDetailPanel: React.FC<NodeDetailPanelProps> = ({ isOpen, onClose }) => {
  const { 
    selectedNodeId, 
    nodes, 
    canEditNode, 
    updateNode, 
    waybills,
    saveNodeChanges 
  } = useAppStore();
  
  const [isEditing, setIsEditing] = useState(false);
  const [editForm, setEditForm] = useState<NodeEditForm | null>(null);
  
  const selectedNode = selectedNodeId ? nodes.find(n => n.id === selectedNodeId) : null;
  const canEdit = selectedNodeId ? canEditNode(selectedNodeId) : false;
  
  // 获取当前节点的运单列表
  const nodeWaybills = waybills.filter(w => w.currentNodeId === selectedNodeId);
  
  useEffect(() => {
    if (selectedNode && isEditing) {
      setEditForm({
        name: selectedNode.name,
        description: selectedNode.description,
        type: selectedNode.type,
        permissions: selectedNode.permissions,
        style: {
          backgroundColor: selectedNode.style?.backgroundColor || '#3B82F6',
          borderColor: selectedNode.style?.borderColor || '#2563EB',
          textColor: selectedNode.style?.textColor || '#FFFFFF'
        }
      });
    }
  }, [selectedNode, isEditing]);
  
  const handleEdit = () => {
    setIsEditing(true);
  };
  
  const handleSave = async () => {
    if (selectedNodeId && editForm) {
      try {
        const result = await saveNodeChanges(selectedNodeId, {
          name: editForm.name,
          description: editForm.description,
          type: editForm.type,
          permissions: editForm.permissions,
          style: editForm.style
        });
        
        if (result.success) {
          setIsEditing(false);
        } else {
          console.error('保存失败:', result.error);
          // 这里可以添加错误提示
        }
      } catch (error) {
        console.error('保存节点时发生错误:', error);
      }
    }
  };
  
  const handleCancel = () => {
    setIsEditing(false);
    setEditForm(null);
  };
  
  if (!isOpen || !selectedNode) {
    return null;
  }
  
  return (
    <div className="fixed inset-y-0 right-0 w-96 bg-white shadow-xl border-l border-gray-200 z-50 overflow-y-auto">
      {/* 头部 */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <h2 className="text-lg font-semibold text-gray-900">
          {isEditing ? '编辑节点' : '节点详情'}
        </h2>
        <div className="flex items-center space-x-2">
          {canEdit && !isEditing && (
            <button
              onClick={handleEdit}
              className="p-2 text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
            >
              <Edit3 className="w-4 h-4" />
            </button>
          )}
          <button
            onClick={onClose}
            className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X className="w-4 h-4" />
          </button>
        </div>
      </div>
      
      <div className="p-4 space-y-6">
        {/* 基本信息 */}
        <div className="space-y-4">
          <h3 className="text-sm font-medium text-gray-900">基本信息</h3>
          
          {isEditing && editForm ? (
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  节点名称
                </label>
                <input
                  type="text"
                  value={editForm.name}
                  onChange={(e) => setEditForm({ ...editForm, name: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  描述
                </label>
                <textarea
                  value={editForm.description}
                  onChange={(e) => setEditForm({ ...editForm, description: e.target.value })}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  背景颜色
                </label>
                <input
                  type="color"
                  value={editForm.style.backgroundColor}
                  onChange={(e) => setEditForm({ 
                    ...editForm, 
                    style: { ...editForm.style, backgroundColor: e.target.value }
                  })}
                  className="w-full h-10 border border-gray-300 rounded-lg"
                />
              </div>
              
              <div className="flex space-x-2">
                <button
                  onClick={handleSave}
                  className="flex-1 flex items-center justify-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  <Save className="w-4 h-4" />
                  <span>保存</span>
                </button>
                <button
                  onClick={handleCancel}
                  className="flex-1 flex items-center justify-center space-x-2 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                >
                  <X className="w-4 h-4" />
                  <span>取消</span>
                </button>
              </div>
            </div>
          ) : (
            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <div 
                  className="w-4 h-4 rounded"
                  style={{ backgroundColor: selectedNode.style?.backgroundColor || '#3B82F6' }}
                />
                <div>
                  <div className="font-medium text-gray-900">{selectedNode.name}</div>
                  <div className="text-sm text-gray-500">{selectedNode.description}</div>
                </div>
              </div>
              
              <div className="text-sm text-gray-600">
                <span className="font-medium">类型:</span> {selectedNode.type}
              </div>
              
              <div className="text-sm text-gray-600">
                <span className="font-medium">状态:</span> 
                <span className={`ml-1 px-2 py-1 rounded-full text-xs ${
                  selectedNode.status === 'completed' ? 'bg-green-100 text-green-800' :
                  selectedNode.status === 'processing' ? 'bg-blue-100 text-blue-800' :
                  selectedNode.status === 'warning' ? 'bg-orange-100 text-orange-800' :
                  selectedNode.status === 'error' ? 'bg-red-100 text-red-800' :
                  'bg-gray-100 text-gray-800'
                }`}>
                  {selectedNode.status === 'completed' ? '已完成' :
                   selectedNode.status === 'processing' ? '处理中' :
                   selectedNode.status === 'warning' ? '警告' :
                   selectedNode.status === 'error' ? '异常' : '待处理'}
                </span>
              </div>
            </div>
          )}
        </div>
        
        {/* 统计数据 */}
        {!isEditing && (
          <div className="space-y-4">
            <h3 className="text-sm font-medium text-gray-900">统计数据</h3>
            
            <div className="bg-blue-50 p-4 rounded-lg">
              <div className="flex items-center space-x-2 text-blue-600 mb-2">
                <Users className="w-5 h-5" />
                <span className="text-sm font-medium">运单数量</span>
              </div>
              <div className="text-3xl font-bold text-blue-900">
                {selectedNode.data.count}
              </div>
            </div>
            
            <div className="text-xs text-gray-500">
              最后更新: {selectedNode.data.lastUpdated}
            </div>
          </div>
        )}
        
        {/* 权限设置 */}
        {!isEditing && (
          <div className="space-y-4">
            <h3 className="text-sm font-medium text-gray-900">权限设置</h3>
            
            <div className="space-y-3">
              <div>
                <div className="flex items-center space-x-2 text-sm font-medium text-gray-700 mb-2">
                  <Eye className="w-4 h-4" />
                  <span>可查看角色</span>
                </div>
                <div className="flex flex-wrap gap-2">
                  {selectedNode.permissions.viewRoles.map(role => (
                    <span key={role} className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                      {role === 'admin' ? '管理员' : role === 'dispatcher' ? '调度员' : '客户'}
                    </span>
                  ))}
                </div>
              </div>
              
              <div>
                <div className="flex items-center space-x-2 text-sm font-medium text-gray-700 mb-2">
                  <Edit3 className="w-4 h-4" />
                  <span>可编辑角色</span>
                </div>
                <div className="flex flex-wrap gap-2">
                  {selectedNode.permissions.editRoles.map(role => (
                    <span key={role} className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                      {role === 'admin' ? '管理员' : role === 'dispatcher' ? '调度员' : '客户'}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}
        
        {/* 当前运单 */}
        {!isEditing && nodeWaybills.length > 0 && (
          <div className="space-y-4">
            <h3 className="text-sm font-medium text-gray-900">当前运单 ({nodeWaybills.length})</h3>
            
            <div className="space-y-2 max-h-60 overflow-y-auto">
              {nodeWaybills.slice(0, 5).map(waybill => (
                <div key={waybill.id} className="p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center justify-between mb-1">
                    <span className="font-medium text-sm">{waybill.orderNumber}</span>
                    <span className={`px-2 py-1 rounded-full text-xs ${
                      waybill.status === 'completed' ? 'bg-green-100 text-green-800' :
                      waybill.status === 'processing' ? 'bg-blue-100 text-blue-800' :
                      waybill.status === 'error' ? 'bg-red-100 text-red-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {waybill.status === 'completed' ? '已完成' :
                       waybill.status === 'processing' ? '处理中' :
                       waybill.status === 'error' ? '异常' : '待处理'}
                    </span>
                  </div>
                  <div className="text-xs text-gray-600">
                    {waybill.origin} → {waybill.destination}
                  </div>
                  <div className="text-xs text-gray-500 mt-1">
                    更新时间: {new Date(waybill.updateTime).toLocaleString()}
                  </div>
                </div>
              ))}
              {nodeWaybills.length > 5 && (
                <div className="text-center text-sm text-gray-500 py-2">
                  还有 {nodeWaybills.length - 5} 个运单...
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};