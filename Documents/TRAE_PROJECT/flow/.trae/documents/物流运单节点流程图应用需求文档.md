# 物流运单节点流程图应用需求文档

## 1. 产品概述

本产品是一个物流运单节点数据统计和可视化管理系统，以交互式流程图的形式展示物流运单在各个环节的状态和数据。

系统主要解决物流运单状态跟踪和数据统计的可视化问题，为物流管理人员、调度员和客户提供直观的运单流程监控界面。

产品目标是提升物流运营效率，降低人工查询成本，实现运单状态的实时可视化管理。

## 2. 核心功能

### 2.1 用户角色

| 角色 | 注册方式 | 核心权限 |
|------|----------|----------|
| 管理员 | 系统预设账号 | 查看所有节点、编辑所有节点、管理用户权限 |
| 调度员 | 管理员邀请注册 | 查看运营节点、编辑部分节点、查看统计数据 |
| 客户 | 自主注册 | 查看基础节点、查看自己的运单状态 |

### 2.2 功能模块

我们的物流运单节点流程图应用包含以下主要页面：

1. **流程图主页**：节点流程图展示、节点状态统计、实时数据更新
2. **节点编辑页**：节点信息编辑、节点连接关系配置、节点权限设置
3. **数据统计页**：运单统计报表、节点通过率分析、时效性分析
4. **角色管理页**：用户角色配置、节点可见性设置、权限分配

### 2.3 页面详情

| 页面名称 | 模块名称 | 功能描述 |
|----------|----------|----------|
| 流程图主页 | 节点流程图 | 展示完整的物流节点流程图，包含开始、发车、在途、特加、在卸、已卸等状态节点 |
| 流程图主页 | 节点状态显示 | 实时显示各节点的运单数量、通过率、异常情况等统计信息 |
| 流程图主页 | 节点交互 | 点击节点查看详细信息，支持节点状态筛选和搜索功能 |
| 节点编辑页 | 节点信息编辑 | 编辑节点名称、描述、状态类型、处理时长等基础信息 |
| 节点编辑页 | 连接关系配置 | 设置节点间的流转关系，定义节点的前置和后置条件 |
| 节点编辑页 | 权限设置 | 配置不同角色对节点的查看和编辑权限 |
| 数据统计页 | 运单统计 | 显示各节点的运单数量统计、时间分布、完成率等数据 |
| 数据统计页 | 图表展示 | 提供柱状图、饼图、折线图等多种数据可视化方式 |
| 角色管理页 | 用户管理 | 管理用户账号、分配角色、设置权限范围 |
| 角色管理页 | 节点可见性 | 配置不同角色可以查看和操作的节点范围 |

## 3. 核心流程

**管理员流程：**
管理员登录系统后，可以在流程图主页查看完整的物流节点流程，包括所有状态节点和统计数据。可以进入节点编辑页面修改节点信息和连接关系，在角色管理页面配置用户权限和节点可见性。

**调度员流程：**
调度员登录后在流程图主页查看运营相关的节点状态，可以编辑部分节点信息，在数据统计页面查看运营报表和分析数据。

**客户流程：**
客户登录后在流程图主页查看简化版的流程图，主要关注运单的当前状态和预计完成时间。

```mermaid
graph TD
    A[流程图主页] --> B[节点编辑页]
    A --> C[数据统计页]
    A --> D[角色管理页]
    B --> A
    C --> A
    D --> A
```

## 4. 用户界面设计

### 4.1 设计风格

- **主色调**：深蓝色 (#1E3A8A) 作为主色，绿色 (#10B981) 作为成功状态色，橙色 (#F59E0B) 作为警告色
- **按钮样式**：圆角矩形按钮，具有轻微阴影效果，支持悬停状态变化
- **字体**：主要使用 14px 的无衬线字体，标题使用 16-20px 加粗字体
- **布局风格**：卡片式布局，顶部导航栏，左侧功能菜单，主内容区域居中显示
- **图标风格**：使用简洁的线性图标，支持物流相关的专业图标

### 4.2 页面设计概览

| 页面名称 | 模块名称 | UI元素 |
|----------|----------|--------|
| 流程图主页 | 节点流程图 | 使用SVG绘制的流程图，节点采用圆角矩形，连接线使用箭头，支持缩放和拖拽 |
| 流程图主页 | 节点状态显示 | 节点上方显示数字徽章，不同颜色表示不同状态，悬停显示详细信息弹窗 |
| 节点编辑页 | 表单区域 | 使用卡片式表单布局，输入框具有聚焦效果，按钮组水平排列 |
| 数据统计页 | 图表区域 | 使用现代化的图表库，支持交互式图表，配色与主题保持一致 |
| 角色管理页 | 表格和表单 | 使用数据表格展示用户信息，表单采用模态框形式，支持批量操作 |

### 4.3 响应式设计

产品采用桌面优先的设计策略，同时支持平板设备的适配。在移动设备上提供简化版的流程图查看功能，主要支持触摸交互和手势缩放。