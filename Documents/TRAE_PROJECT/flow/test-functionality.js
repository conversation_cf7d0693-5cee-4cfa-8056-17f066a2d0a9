// 功能测试脚本：验证边删除和节点添加功能
console.log('=== 功能测试开始 ===');

// 检查当前状态
function checkCurrentState() {
  const editButton = document.querySelector('button[title*="编辑"]');
  const isInEditMode = editButton && editButton.textContent.includes('退出');
  const edges = document.querySelectorAll('.react-flow__edge');
  const deleteButton = document.querySelector('button[title*="删除选中的连线"]');
  const addNodeButton = document.querySelector('button[title*="直接在画布中心添加节点"]');

  console.log('当前状态:', {
    isInEditMode,
    edgeCount: edges.length,
    hasDeleteButton: !!deleteButton,
    hasAddNodeButton: !!addNodeButton
  });

  return { isInEditMode, edges, deleteButton, addNodeButton };
}

// 进入编辑模式
function enterEditMode() {
  const editButton = document.querySelector('button[title*="编辑"]');
  if (editButton && !editButton.textContent.includes('退出')) {
    console.log('进入编辑模式...');
    editButton.click();
    return true;
  }
  return false;
}

// 测试连线点击
function testEdgeClick() {
  const edges = document.querySelectorAll('.react-flow__edge');
  if (edges.length > 0) {
    console.log(`找到 ${edges.length} 条连线，点击第一条...`);
    const firstEdge = edges[0];
    firstEdge.click();

    setTimeout(() => {
      const deleteButton = document.querySelector('button[title*="删除选中的连线"]');
      if (deleteButton) {
        console.log('✓ 连线选择成功！删除按钮出现');
        console.log('测试删除功能...');
        deleteButton.click();

        setTimeout(() => {
          const deleteButtonAfter = document.querySelector('button[title*="删除选中的连线"]');
          if (!deleteButtonAfter) {
            console.log('✓ 连线删除成功！');
          } else {
            console.log('✗ 连线删除失败');
          }
        }, 1000);
      } else {
        console.log('✗ 连线选择失败，删除按钮未出现');
      }
    }, 500);
  } else {
    console.log('✗ 未找到连线');
  }
}

// 测试新增节点按钮
function testAddNodeButton() {
  const addNodeButton = document.querySelector('button[title*="直接在画布中心添加节点"]');
  if (addNodeButton) {
    console.log('测试新增节点按钮...');
    const nodeCountBefore = document.querySelectorAll('.react-flow__node').length;
    console.log('添加前节点数量:', nodeCountBefore);

    addNodeButton.click();

    setTimeout(() => {
      const nodeCountAfter = document.querySelectorAll('.react-flow__node').length;
      console.log('添加后节点数量:', nodeCountAfter);

      if (nodeCountAfter > nodeCountBefore) {
        console.log('✓ 节点添加成功！');
      } else {
        console.log('✗ 节点添加失败');
      }
    }, 1000);
  } else {
    console.log('✗ 未找到新增节点按钮');
  }
}

// 测试双击添加节点
function testDoubleClick() {
  const reactFlowPane = document.querySelector('.react-flow__pane');
  if (reactFlowPane) {
    console.log('测试双击添加节点...');
    const rect = reactFlowPane.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;

    const event = new MouseEvent('dblclick', {
      bubbles: true,
      cancelable: true,
      clientX: centerX,
      clientY: centerY
    });

    reactFlowPane.dispatchEvent(event);
    console.log('双击事件已触发');
  } else {
    console.log('✗ 未找到画布');
  }
}

// 执行测试
const state = checkCurrentState();
if (!state.isInEditMode) {
  enterEditMode();
  setTimeout(() => {
    testEdgeClick();
    setTimeout(() => {
      testAddNodeButton();
      setTimeout(() => testDoubleClick(), 2000);
    }, 3000);
  }, 1000);
} else {
  testEdgeClick();
  setTimeout(() => {
    testAddNodeButton();
    setTimeout(() => testDoubleClick(), 2000);
  }, 3000);
}

console.log('');
console.log('=== 手动测试步骤 ===');
console.log('1. 确保在编辑模式下');
console.log('2. 点击任意连线，应该变红');
console.log('3. 点击"删除连线"按钮，连线应该消失');
console.log('4. 点击"新增节点"按钮，节点应该立即出现');
console.log('5. 双击画布空白区域，也应该添加新节点');
